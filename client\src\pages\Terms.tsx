import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation, type Language } from "@/lib/i18n";
import { SEOHead } from "@/components/SEOHead";
import { FileText, AlertTriangle, CreditCard, Shield, Users, Gavel, ArrowLeft } from "lucide-react";
import { Link } from "wouter";

interface TermsProps {
  language: Language;
}

export default function Terms({ language }: TermsProps) {
  const { t } = useTranslation(language);

  const sections = [
    {
      id: "acceptance",
      title: "Acceptance of Terms",
      icon: <FileText className="h-6 w-6" />,
      content: `By accessing and using Remove bg ("the Service"), you accept and agree to be bound by the terms and provision of this agreement.

If you do not agree to abide by the above, please do not use this service. These terms apply to all visitors, users, and others who access or use the Service.

We reserve the right to update and change the Terms of Service from time to time without notice. Any new features that augment or enhance the current Service, including the release of new tools and resources, shall be subject to the Terms of Service.`
    },
    {
      id: "description",
      title: "Description of Service",
      icon: <Shield className="h-6 w-6" />,
      content: `Remove bg provides an AI-powered background removal service that allows users to:
• Upload images and automatically remove backgrounds
• Download processed images with transparent backgrounds
• Access our API for automated background removal
• Use various integrations and plugins

The Service is provided "as is" and we make no warranties about the availability, accuracy, or quality of the Service. We reserve the right to modify or discontinue the Service at any time.`
    },
    {
      id: "user-accounts",
      title: "User Accounts and Responsibilities",
      icon: <Users className="h-6 w-6" />,
      content: `When you create an account with us, you must provide information that is accurate, complete, and current at all times.

You are responsible for:
• Safeguarding the password and all activities under your account
• Maintaining the security of your account and password
• Notifying us immediately of any unauthorized use of your account
• All content and activity that occurs under your account

You may not use the Service for any illegal or unauthorized purpose nor may you, in the use of the Service, violate any laws in your jurisdiction.`
    },
    {
      id: "acceptable-use",
      title: "Acceptable Use Policy",
      icon: <AlertTriangle className="h-6 w-6" />,
      content: `You agree not to use the Service to:
• Upload, post, or transmit any content that is illegal, harmful, threatening, abusive, defamatory, or otherwise objectionable
• Violate any laws, regulations, or third-party rights
• Upload images containing copyrighted material without proper authorization
• Attempt to interfere with, compromise the system integrity or security, or decipher any transmissions
• Use automated scripts or bots to access the Service
• Resell or redistribute the Service without explicit permission

Violation of these terms may result in immediate termination of your account and access to the Service.`
    },
    {
      id: "intellectual-property",
      title: "Intellectual Property Rights",
      icon: <Gavel className="h-6 w-6" />,
      content: `The Service and its original content, features, and functionality are and will remain the exclusive property of Remove bg and its licensors.

You retain all rights to images you upload to our Service. By uploading content, you grant us a limited, non-exclusive, royalty-free license to process your images for the purpose of providing the Service.

You represent and warrant that:
• You own or have the necessary rights to use the images you upload
• Your use of the images does not violate any third-party rights
• You have permission to use any copyrighted material in your uploads

We reserve the right to remove any content that we believe violates these terms or any applicable laws.`
    },
    {
      id: "payment-terms",
      title: "Payment and Billing",
      icon: <CreditCard className="h-6 w-6" />,
      content: `Some features of the Service require payment. By purchasing a paid plan, you agree to pay all charges associated with your account.

Payment terms:
• All fees are in US Dollars unless otherwise specified
• Payment is due immediately upon purchase
• Subscription fees are billed in advance on a recurring basis
• We reserve the right to change our pricing with 30 days notice
• Refunds are provided according to our refund policy

You are responsible for providing current, complete, and accurate purchase and account information for all purchases made via the Service.`
    },
    {
      id: "limitation",
      title: "Limitation of Liability",
      icon: <Shield className="h-6 w-6" />,
      content: `In no event shall Remove bg, its directors, employees, partners, agents, suppliers, or affiliates be liable for any indirect, incidental, special, consequential, or punitive damages, including loss of profits, data, use, goodwill, or other intangible losses.

The Service is provided on an "AS IS" and "AS AVAILABLE" basis. We disclaim all warranties of any kind, whether express or implied, including warranties of merchantability, fitness for a particular purpose, and non-infringement.

Some jurisdictions do not allow the exclusion or limitation of liability for consequential or incidental damages, so the above limitation may not apply to you.`
    },
    {
      id: "termination",
      title: "Termination",
      icon: <AlertTriangle className="h-6 w-6" />,
      content: `We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever, including:
• Breach of the Terms of Service
• Violation of our Acceptable Use Policy
• Extended periods of inactivity
• Legal or regulatory requirements

Upon termination, your right to use the Service will cease immediately. If you wish to terminate your account, you may simply discontinue using the Service.

All provisions of the Terms which by their nature should survive termination shall survive termination, including ownership provisions, warranty disclaimers, indemnity, and limitations of liability.`
    }
  ];

  return (
    <>
      <SEOHead
        title="Terms of Service - Remove bg"
        description="Read our Terms of Service to understand the rules and guidelines for using Remove bg's AI-powered background removal service."
        language={language}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="py-16 lg:py-24 bg-white">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Back Button */}
            <div className="mb-8">
              <Link href="/">
                <Button variant="ghost" className="gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  返回首页
                </Button>
              </Link>
            </div>
            <div className="text-center mb-16">
              <div className="gradient-bg rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <FileText className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                Terms of Service
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Please read these Terms of Service carefully before using our service. By using Remove bg, you agree to these terms.
              </p>
              <div className="mt-6 text-sm text-gray-500">
                <p>Last updated: December 29, 2024</p>
                <p>Effective date: January 1, 2025</p>
              </div>
            </div>
          </div>
        </section>

        {/* Terms Sections */}
        <section className="py-16">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            {/* Quick Summary */}
            <Card className="mb-12 shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl gradient-text">Quick Summary</CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600 space-y-4">
                <p>
                  <strong>TL;DR:</strong> By using Remove bg, you agree to:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Use our service responsibly and legally</li>
                  <li>Only upload images you have rights to use</li>
                  <li>Respect our intellectual property and that of others</li>
                  <li>Pay for premium features if you use them</li>
                  <li>Accept that we provide the service "as is" without guarantees</li>
                  <li>Allow us to terminate access if you violate these terms</li>
                </ul>
                <p className="text-sm text-gray-500 italic">
                  This summary is for convenience only. Please read the full terms below for complete details.
                </p>
              </CardContent>
            </Card>

            {/* Detailed Sections */}
            <div className="space-y-8">
              {sections.map((section, index) => (
                <Card key={section.id} className="shadow-lg">
                  <CardHeader>
                    <div className="flex items-center mb-4">
                      <div className="gradient-bg rounded-lg p-3 text-white mr-4">
                        {section.icon}
                      </div>
                      <CardTitle className="text-xl text-gray-900">
                        {index + 1}. {section.title}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-gray-600 whitespace-pre-line leading-relaxed">
                      {section.content}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Information */}
            <Card className="mt-12 shadow-lg border-l-4 border-purple-500">
              <CardHeader>
                <CardTitle className="text-xl text-purple-700">
                  Questions About These Terms?
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  If you have any questions about these Terms of Service, please contact us:
                </p>
                <div className="mt-4 space-y-2">
                  <p><strong>Email:</strong> <EMAIL></p>
                  <p><strong>Address:</strong> Remove bg Legal Team<br />
                  123 AI Technology Street<br />
                  San Francisco, CA 94105<br />
                  United States</p>
                </div>
                <p className="mt-4 text-sm">
                  We will respond to your inquiry within 5 business days.
                </p>
              </CardContent>
            </Card>

            {/* Governing Law */}
            <Card className="mt-8 shadow-lg border-l-4 border-blue-500">
              <CardHeader>
                <CardTitle className="text-xl text-blue-700">
                  Governing Law
                </CardTitle>
              </CardHeader>
              <CardContent className="text-gray-600">
                <p>
                  These Terms shall be interpreted and governed by the laws of the State of California, United States, 
                  without regard to its conflict of law provisions.
                </p>
                <p className="mt-4">
                  Any disputes arising from these Terms or your use of the Service will be resolved through binding 
                  arbitration in accordance with the rules of the American Arbitration Association.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </>
  );
}
